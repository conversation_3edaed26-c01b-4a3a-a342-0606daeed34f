import { useState, useCallback } from 'react';
import { message } from 'antd';
import { MESSAGES } from '../constants';

export const useDictSelection = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<number | undefined>(undefined);

  // 处理选择变化
  const handleSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  }, []);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  // 搜索处理
  const handleSearch = useCallback((value: string) => {
    setSearchKeyword(value);
  }, []);

  // 状态筛选处理
  const handleStatusFilterChange = useCallback((value: number | undefined) => {
    setStatusFilter(value);
  }, []);

  // 重置筛选条件
  const resetFilters = useCallback(() => {
    setSearchKeyword('');
    setStatusFilter(undefined);
    setSelectedRowKeys([]);
  }, []);

  // 检查是否有选中项
  const checkSelection = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      message.warning(MESSAGES.warning.selectItems);
      return false;
    }
    return true;
  }, [selectedRowKeys.length]);

  // 获取选中的ID数组
  const getSelectedIds = useCallback(() => {
    return selectedRowKeys.map((key) => Number(key));
  }, [selectedRowKeys]);

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: handleSelectChange,
  };

  return {
    selectedRowKeys,
    searchKeyword,
    statusFilter,
    rowSelection,
    handleSelectChange,
    clearSelection,
    handleSearch,
    handleStatusFilterChange,
    resetFilters,
    checkSelection,
    getSelectedIds,
  };
};
