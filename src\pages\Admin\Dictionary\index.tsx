/**
 * @file 字典管理主页面
 * @description 字典管理功能的主入口，整合了区域字典、类型字典、关系字典的管理功能
 * @features
 *   - 支持三种字典类型的增删改查
 *   - 支持批量操作和状态切换
 *   - 支持搜索和筛选
 *   - 支持树形结构的父子关系
 * <AUTHOR> Assistant
 * @date 2025-08-29
 * @version 2.0.0 (重构版本)
 */

import React, { useEffect } from 'react';
import { Tabs, Typography } from 'antd';
import type { DictType } from './dict-types';
import { useDictData } from './hooks/useDictData';
import { useDictOperations } from './hooks/useDictOperations';
import { useDictForm } from './hooks/useDictForm';
import { useDictSelection } from './hooks/useDictSelection';
import { DictToolbar } from './components/DictToolbar';
import { DictTable } from './components/DictTable';
import { DictModal } from './components/DictModal';

const { Title } = Typography;

const AdminDictionary: React.FC = () => {
  // 使用自定义Hook管理状态和操作
  const {
    regionData,
    typeData,
    relationData,
    loading,
    parentOptions,
    loadData,
    loadParentOptions,
  } = useDictData();

  const {
    loading: operationLoading,
    batchLoading,
    createDict,
    updateDict,
    deleteDict,
    toggleStatus,
    batchUpdateStatus,
  } = useDictOperations();

  const {
    form,
    modalVisible,
    editingItem,
    currentTab,
    setCurrentTab,
    openAddModal,
    openEditModal,
    closeModal,
    getModalTitle,
    validateAndGetValues,
  } = useDictForm();

  const {
    selectedRowKeys,
    searchKeyword,
    statusFilter,
    rowSelection,
    handleSearch,
    handleStatusFilterChange,
    resetFilters,
    checkSelection,
    getSelectedIds,
    clearSelection,
  } = useDictSelection();



  // 初始化数据
  useEffect(() => {
    loadData(currentTab as DictType);
  }, [currentTab, searchKeyword, statusFilter, loadData]);

  // 处理函数
  const handleAdd = (type: DictType) => {
    loadParentOptions(type);
    openAddModal(type);
  };

  const handleEdit = (record: any, type: DictType) => {
    loadParentOptions(type, record.id);
    openEditModal(record, type);
  };

  const handleDelete = async (id: number, type: DictType) => {
    const result = await deleteDict(type, id);
    if (result.success) {
      loadData(type);
    }
  };

  const handleSubmit = async () => {
    const { success, values } = await validateAndGetValues();
    if (!success) return;

    let result;
    if (editingItem) {
      result = await updateDict(currentTab, editingItem.id, values);
    } else {
      result = await createDict(currentTab, values);
    }

    if (result.success) {
      closeModal();
      loadData(currentTab);
    }
  };

  const handleStatusToggle = async (id: number, type: DictType) => {
    const result = await toggleStatus(type, id);
    if (result.success) {
      loadData(type);
    }
  };

  // 批量操作函数
  const handleBatchStatusUpdate = async (status: number) => {
    if (!checkSelection()) return;

    const ids = getSelectedIds();
    const result = await batchUpdateStatus(currentTab, { ids, status });
    if (result.success) {
      clearSelection();
      loadData(currentTab);
    }
  };

  // 刷新处理
  const handleRefresh = (type: DictType) => {
    loadData(type);
    resetFilters();
  };













  const tabItems = [
    {
      key: 'region',
      label: '区域字典',
      children: (
        <div>
          <DictToolbar
            type="region"
            searchKeyword={searchKeyword}
            statusFilter={statusFilter}
            selectedRowKeys={selectedRowKeys}
            batchLoading={batchLoading}
            onAdd={handleAdd}
            onSearch={handleSearch}
            onStatusFilterChange={handleStatusFilterChange}
            onRefresh={handleRefresh}
            onBatchStatusUpdate={handleBatchStatusUpdate}
            onClearSelection={clearSelection}
          />
          <DictTable
            type="region"
            data={regionData}
            loading={loading}
            selectedRowKeys={selectedRowKeys}
            onSelectChange={rowSelection.onChange}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onStatusToggle={handleStatusToggle}
          />
        </div>
      ),
    },
    {
      key: 'type',
      label: '类型字典',
      children: (
        <div>
          <DictToolbar
            type="type"
            searchKeyword={searchKeyword}
            statusFilter={statusFilter}
            selectedRowKeys={selectedRowKeys}
            batchLoading={batchLoading}
            onAdd={handleAdd}
            onSearch={handleSearch}
            onStatusFilterChange={handleStatusFilterChange}
            onRefresh={handleRefresh}
            onBatchStatusUpdate={handleBatchStatusUpdate}
            onClearSelection={clearSelection}
          />
          <DictTable
            type="type"
            data={typeData}
            loading={loading}
            selectedRowKeys={selectedRowKeys}
            onSelectChange={rowSelection.onChange}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onStatusToggle={handleStatusToggle}
          />
        </div>
      ),
    },
    {
      key: 'relation',
      label: '关系字典',
      children: (
        <div>
          <DictToolbar
            type="relation"
            searchKeyword={searchKeyword}
            statusFilter={statusFilter}
            selectedRowKeys={selectedRowKeys}
            batchLoading={batchLoading}
            onAdd={handleAdd}
            onSearch={handleSearch}
            onStatusFilterChange={handleStatusFilterChange}
            onRefresh={handleRefresh}
            onBatchStatusUpdate={handleBatchStatusUpdate}
            onClearSelection={clearSelection}
          />
          <DictTable
            type="relation"
            data={relationData}
            loading={loading}
            selectedRowKeys={selectedRowKeys}
            onSelectChange={rowSelection.onChange}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onStatusToggle={handleStatusToggle}
          />
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        字典管理
      </Title>

      <Tabs
        items={tabItems}
        activeKey={currentTab}
        onChange={(key) => {
          setCurrentTab(key as DictType);
          resetFilters();
        }}
        tabBarStyle={{ marginBottom: 16 }}
      />

      <DictModal
        visible={modalVisible}
        title={getModalTitle()}
        loading={operationLoading}
        form={form}
        type={currentTab}
        parentOptions={parentOptions}
        onOk={handleSubmit}
        onCancel={closeModal}
      />
    </div>
  );
};

export default AdminDictionary;
