import PhotoStatisticsComponent from '@/components/PhotoStatistics';
import {
  historicalElementData,
  mountainData,
  waterSystemData,
} from '@/services/mockData';
import {
  deletePhoto,
  getPhotoList,
  getPhotoStatistics,
  updatePhoto,
  uploadFile,
  uploadUtils,
  type PhotoRecord,
  type PhotoStatistics,
} from '@/services/upload';
// TODO 后面要使用model维护真实数据
import { regionDict } from '@/services/mockData';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import {
  Button,
  Card,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Tooltip,
  TreeSelect,
  Typography,
  Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Title } = Typography;
const { Dragger } = Upload;

const AdminUpload: React.FC = () => {
  const [data, setData] = useState<PhotoRecord[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<PhotoRecord | null>(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<PhotoStatistics | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState('');

  // 上传配置状态 - 改为TreeSelect格式
  const [uploadConfig, setUploadConfig] = useState({
    selectedValue: undefined as string | undefined, // TreeSelect的值，格式如 "region-1-mountain-2"
    entityType: undefined as
      | 'mountain'
      | 'waterSystem'
      | 'historicalElement'
      | undefined,
    entityId: undefined as number | undefined,
  });

  // 构建TreeSelect数据结构
  const buildTreeSelectData = () => {
    return regionDict.map((region) => ({
      title: region.region_name,
      value: `region-${region.id}`,
      key: `region-${region.id}`,
      selectable: false, // 区域不可选择
      children: [
        {
          title: '山塬',
          value: `region-${region.id}-mountain`,
          key: `region-${region.id}-mountain`,
          selectable: false, // 类别不可选择
          children: mountainData
            .filter((item) => item.region_dict_id === region.id)
            .map((mountain) => ({
              title: mountain.name,
              value: `region-${region.id}-mountain-${mountain.id}`,
              key: `region-${region.id}-mountain-${mountain.id}`,
              selectable: true, // 具体项目可选择
            })),
        },
        {
          title: '水系',
          value: `region-${region.id}-waterSystem`,
          key: `region-${region.id}-waterSystem`,
          selectable: false, // 类别不可选择
          children: waterSystemData
            .filter((item) => item.region_dict_id === region.id)
            .map((waterSystem) => ({
              title: waterSystem.name,
              value: `region-${region.id}-waterSystem-${waterSystem.id}`,
              key: `region-${region.id}-waterSystem-${waterSystem.id}`,
              selectable: true, // 具体项目可选择
            })),
        },
        {
          title: '历史要素',
          value: `region-${region.id}-historicalElement`,
          key: `region-${region.id}-historicalElement`,
          selectable: false, // 类别不可选择
          children: historicalElementData
            .filter((item) => item.region_dict_id === region.id)
            .map((element) => ({
              title: element.name,
              value: `region-${region.id}-historicalElement-${element.id}`,
              key: `region-${region.id}-historicalElement-${element.id}`,
              selectable: true, // 具体项目可选择
            })),
        },
      ].filter((category) => category.children.length > 0), // 只显示有数据的类别
    }));
  };

  // 解析TreeSelect选择的值
  const parseSelectedValue = (value: string) => {
    if (!value) return { entityType: undefined, entityId: undefined };

    const parts = value.split('-');
    if (parts.length !== 4)
      return { entityType: undefined, entityId: undefined };

    const [, , entityType, entityId] = parts;
    return {
      entityType: entityType as
        | 'mountain'
        | 'waterSystem'
        | 'historicalElement',
      entityId: parseInt(entityId, 10),
    };
  };

  // 加载照片列表
  const loadPhotoList = async (page = 1, pageSize = 10, keyword = '') => {
    try {
      setLoading(true);
      const response = await getPhotoList({
        page,
        pageSize,
        keyword: keyword || undefined,
      });

      if (response.errCode === 0 && response.data) {
        setData(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      console.error('加载照片列表失败:', error);
      message.error('加载照片列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await getPhotoStatistics();
      if (response.errCode === 0 && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadPhotoList();
    loadStatistics();
  }, []);

  const handleEdit = (record: PhotoRecord) => {
    setEditingItem(record);
    form.setFieldsValue({
      name: record.name,
      mountainId: record.mountainId,
      waterSystemId: record.waterSystemId,
      historicalElementId: record.historicalElementId,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deletePhoto(id);
      if (response.errCode === 0) {
        message.success('删除成功！');
        loadPhotoList(pagination.current, pagination.pageSize, searchKeyword);
        loadStatistics();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        const response = await updatePhoto(editingItem.id, values);
        if (response.errCode === 0) {
          message.success('编辑成功！');
          setModalVisible(false);
          loadPhotoList(pagination.current, pagination.pageSize, searchKeyword);
          loadStatistics();
        } else {
          message.error(response.msg || '编辑失败');
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '预览',
      dataIndex: 'url',
      key: 'preview',
      width: 100,
      render: (url: string) => (
        <Image
          width={60}
          height={40}
          src={url}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          preview={{
            mask: <EyeOutlined />,
          }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '文件路径',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
    },
    {
      title: '关联对象',
      key: 'related',
      render: (_: any, record: PhotoRecord) => {
        if (record.mountainId) {
          if (record.mountain) {
            return `山塬: ${record.mountain.name}`;
          }
          const mountain = mountainData.find((m) => m.id === record.mountainId);
          return `山塬: ${mountain?.name || '未知'}`;
        }
        if (record.waterSystemId) {
          if (record.waterSystem) {
            return `水系: ${record.waterSystem.name}`;
          }
          const waterSystem = waterSystemData.find(
            (w) => w.id === record.waterSystemId,
          );
          return `水系: ${waterSystem?.name || '未知'}`;
        }
        if (record.historicalElementId) {
          if (record.historicalElement) {
            return `历史要素: ${record.historicalElement.name}`;
          }
          const element = historicalElementData.find(
            (h) => h.id === record.historicalElementId,
          );
          return `历史要素: ${element?.name || '未知'}`;
        }
        return '无关联';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (createdAt: string) => {
        return createdAt ? new Date(createdAt).toLocaleString() : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            style={{ padding: 0 }}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个文件吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 自定义上传函数
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    // 验证文件
    const validation = uploadUtils.validateFile(file);
    if (!validation.valid) {
      message.error(validation.message);
      onError(new Error(validation.message));
      return;
    }

    try {
      setUploading(true);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const current = prev[file.uid] || 0;
          const next = Math.min(current + Math.random() * 30, 90);
          onProgress({ percent: next });
          return { ...prev, [file.uid]: next };
        });
      }, 200);

      // 调用上传接口，使用配置的参数
      const response = await uploadFile(file, {
        photoName: file.name,
        entityType: uploadConfig.entityType,
        entityId: uploadConfig.entityId,
      });

      clearInterval(progressInterval);

      if (response.errCode === 0 && response.data) {
        // 上传成功
        setUploadProgress((prev) => ({ ...prev, [file.uid]: 100 }));
        onProgress({ percent: 100 });
        onSuccess(response.data);

        const successMsg = response.data.photoId
          ? `${file.name} 上传成功，已创建照片记录 #${response.data.photoId}`
          : `${file.name} 上传成功`;
        message.success(successMsg);

        // 重新加载照片列表和统计信息
        loadPhotoList(pagination.current, pagination.pageSize, searchKeyword);
        loadStatistics();

        // 清除进度
        setTimeout(() => {
          setUploadProgress((prev) => {
            const newProgress = { ...prev };
            delete newProgress[file.uid];
            return newProgress;
          });
        }, 1000);
      } else {
        throw new Error(response.msg || '上传失败');
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      message.error(error.message || `${file.name} 上传失败`);
      onError(error);
    } finally {
      setUploading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    customRequest: customUpload,
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onRemove: (file) => {
      setFileList((prev) => prev.filter((item) => item.uid !== file.uid));
    },
    beforeUpload: (file) => {
      const validation = uploadUtils.validateFile(file);
      if (!validation.valid) {
        message.error(validation.message);
        return false;
      }
      return true;
    },
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
  };

  return (
    <div className="upload-container" style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        资源管理
      </Title>

      {/* 统计信息 */}
      <div style={{ marginBottom: 24 }}>
        <PhotoStatisticsComponent statistics={statistics} />
      </div>

      {/* 上传配置 */}
      <Card title="上传配置" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Space align="center" wrap>
              <span>关联对象：</span>
              <TreeSelect
                placeholder="选择区域 > 类别 > 具体项目"
                style={{
                  width: 300,
                  zIndex: 1000,
                }}
                value={uploadConfig.selectedValue}
                onChange={(value) => {
                  const parsed = parseSelectedValue(value);
                  setUploadConfig((prev) => ({
                    ...prev,
                    selectedValue: value,
                    entityType: parsed.entityType,
                    entityId: parsed.entityId,
                  }));
                }}
                treeData={buildTreeSelectData()}
                treeCheckable={false}
                showCheckedStrategy={TreeSelect.SHOW_CHILD}
                allowClear
                treeDefaultExpandAll={false}
                treeNodeFilterProp="title"
                showSearch
                filterTreeNode={(input, node) => {
                  return (node.title as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                getPopupContainer={(triggerNode) =>
                  triggerNode.parentElement || document.body
                }
              />

              <span style={{ color: '#666', fontSize: '12px' }}>
                {uploadConfig.entityType && uploadConfig.entityId
                  ? '上传时会自动关联到选择的对象'
                  : '若需要直接关联对象，请按层级选择：区域 → 类别 → 具体项目'}
              </span>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 上传区域 */}
      <Card
        title={
          <div>
            文件上传{' '}
            <Tooltip
              title={
                <div className="upload-tips">
                  <div className="tips-title">📋 上传说明</div>
                  <ul className="tips-list">
                    <li>支持的图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP</li>
                    <li>单个文件最大大小：50MB</li>
                    <li>上传成功后可以编辑文件信息并关联到相关对象</li>
                    <li>文件将自动按日期分类存储</li>
                  </ul>
                </div>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        }
        style={{ marginBottom: 24 }}
      >
        <Dragger {...uploadProps} style={{ padding: '20px' }}>
          <p className="ant-upload-drag-icon">
            {uploading ? <LoadingOutlined /> : <InboxOutlined />}
          </p>
          <p className="ant-upload-text">
            {uploading ? '正在上传...' : '点击或拖拽文件到此区域上传'}
          </p>
          <p className="ant-upload-hint">
            支持单个或批量上传。仅支持图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP
            <br />
            单个文件最大 50MB
          </p>
        </Dragger>

        {/* 上传进度显示 */}
        {Object.keys(uploadProgress).length > 0 && (
          <div className="upload-progress">
            {Object.entries(uploadProgress).map(([uid, progress]) => {
              const file = fileList.find((f) => f.uid === uid);
              return file ? (
                <div key={uid} className="progress-item">
                  <div className="file-info">
                    <span className="file-name">{file.name}</span>
                    <span className="file-size">
                      {uploadUtils.formatFileSize(file.size || 0)}
                    </span>
                  </div>
                  <Progress percent={Math.round(progress)} size="small" />
                </div>
              ) : null;
            })}
          </div>
        )}
      </Card>

      {/* 照片列表 */}
      <Card
        title="照片列表"
        extra={
          <Space>
            <Input.Search
              placeholder="搜索照片名称"
              allowClear
              style={{ width: 200 }}
              onSearch={(value) => {
                setSearchKeyword(value);
                loadPhotoList(1, pagination.pageSize, value);
              }}
            />
            <Button
              type="default"
              onClick={() => {
                loadPhotoList();
                loadStatistics();
              }}
              size="small"
            >
              刷新数据
            </Button>
          </Space>
        }
      >
        <Table
          className="file-table"
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 张照片`,
            onChange: (page, pageSize) => {
              loadPhotoList(page, pageSize, searchKeyword);
            },
            onShowSizeChange: (_, size) => {
              loadPhotoList(1, size, searchKeyword);
            },
          }}
          size="middle"
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑照片信息"
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="照片名称"
            rules={[{ required: true, message: '请输入照片名称' }]}
          >
            <Input placeholder="请输入照片名称" />
          </Form.Item>

          <Form.Item name="mountainId" label="关联山塬">
            <Select placeholder="请选择关联的山塬" allowClear>
              {mountainData.map((mountain) => (
                <Select.Option key={mountain.id} value={mountain.id}>
                  {mountain.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="waterSystemId" label="关联水系">
            <Select placeholder="请选择关联的水系" allowClear>
              {waterSystemData.map((waterSystem) => (
                <Select.Option key={waterSystem.id} value={waterSystem.id}>
                  {waterSystem.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="historicalElementId" label="关联历史要素">
            <Select placeholder="请选择关联的历史要素" allowClear>
              {historicalElementData.map((element) => (
                <Select.Option key={element.id} value={element.id}>
                  {element.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUpload;
