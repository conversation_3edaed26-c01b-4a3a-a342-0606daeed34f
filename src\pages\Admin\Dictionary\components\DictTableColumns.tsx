import React from 'react';
import { <PERSON><PERSON>, Badge, Space, Switch, Popconfirm } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import type { DictType, RegionDict, TypeDict, RelationshipDict } from '../dict-types';
import { COLUMN_WIDTHS, MESSAGES } from '../constants';

interface DictTableColumnsProps {
  type: DictType;
  onEdit: (record: any, type: DictType) => void;
  onDelete: (id: number, type: DictType) => void;
  onStatusToggle: (id: number, type: DictType) => void;
}

export const useDictTableColumns = ({
  type,
  onEdit,
  onDelete,
  onStatusToggle,
}: DictTableColumnsProps) => {
  // 基础列配置
  const baseColumns = [
    { 
      title: 'ID', 
      dataIndex: 'id', 
      key: 'id', 
      width: COLUMN_WIDTHS.id 
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: COLUMN_WIDTHS.sort,
      align: 'center' as const,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: COLUMN_WIDTHS.status,
      render: (status: number, record: any) => (
        <Space>
          <Badge status={status === 1 ? 'success' : 'default'} />
          <Switch
            checked={status === 1}
            onChange={() => onStatusToggle(record.id, type)}
            size="small"
          />
        </Space>
      ),
    },
  ];

  // 操作列
  const actionColumn = {
    title: '操作',
    key: 'action',
    align: 'center' as const,
    width: COLUMN_WIDTHS.action,
    render: (_: any, record: any) => (
      <Space size="middle">
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => onEdit(record, type)}
          style={{ padding: 0 }}
        >
          编辑
        </Button>
        <Popconfirm
          title={MESSAGES.confirm.delete}
          onConfirm={() => onDelete(record.id, type)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            style={{ padding: 0 }}
          >
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  };

  // 根据类型返回不同的列配置
  const getColumns = (): TableColumnsType<any> => {
    switch (type) {
      case 'region':
        return [
          baseColumns[0], // ID
          {
            title: '区域编码',
            dataIndex: 'regionCode',
            key: 'regionCode',
            width: COLUMN_WIDTHS.code,
          },
          { title: '区域名称', dataIndex: 'regionName', key: 'regionName' },
          baseColumns[1], // 排序
          baseColumns[2], // 状态
          {
            title: '描述',
            dataIndex: 'regionDesc',
            key: 'regionDesc',
            ellipsis: true,
          },
          actionColumn,
        ] as TableColumnsType<RegionDict>;

      case 'type':
        return [
          baseColumns[0], // ID
          { 
            title: '类型编码', 
            dataIndex: 'typeCode', 
            key: 'typeCode', 
            width: COLUMN_WIDTHS.code 
          },
          { title: '类型名称', dataIndex: 'typeName', key: 'typeName' },
          baseColumns[1], // 排序
          baseColumns[2], // 状态
          { 
            title: '描述', 
            dataIndex: 'typeDesc', 
            key: 'typeDesc', 
            ellipsis: true 
          },
          actionColumn,
        ] as TableColumnsType<TypeDict>;

      case 'relation':
        return [
          baseColumns[0], // ID
          {
            title: '关系编码',
            dataIndex: 'relationCode',
            key: 'relationCode',
            width: COLUMN_WIDTHS.code,
          },
          { title: '关系名称', dataIndex: 'relationName', key: 'relationName' },
          baseColumns[1], // 排序
          baseColumns[2], // 状态
          {
            title: '描述',
            dataIndex: 'relationDesc',
            key: 'relationDesc',
            ellipsis: true,
          },
          actionColumn,
        ] as TableColumnsType<RelationshipDict>;

      default:
        return [];
    }
  };

  return getColumns();
};
