/**
 * @file 字典管理相关的TypeScript类型定义
 * @description 定义字典管理功能中使用的所有接口、类型和枚举
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

// 字典类型枚举
export type DictType = 'region' | 'type' | 'relation';

// 基础字典接口
export interface BaseDict {
  id: number;
  parentId?: number | null;
  sort: number;
  status: number;
  children?: BaseDict[];
}

// 区域字典接口
export interface RegionDict extends BaseDict {
  regionCode: string;
  regionName: string;
  regionDesc?: string;
}

// 类型字典接口
export interface TypeDict extends BaseDict {
  typeCode: string;
  typeName: string;
  typeDesc?: string;
}

// 关系字典接口
export interface RelationshipDict extends BaseDict {
  relationCode: string;
  relationName: string;
  relationDesc?: string;
}

// 联合字典类型
export type DictItem = RegionDict | TypeDict | RelationshipDict;

// 表单数据接口
export interface DictFormData {
  id?: number;
  parentId?: number | null;
  sort: number;
  status: number;
  // 区域字典字段
  regionCode?: string;
  regionName?: string;
  regionDesc?: string;
  // 类型字典字段
  typeCode?: string;
  typeName?: string;
  typeDesc?: string;
  // 关系字典字段
  relationCode?: string;
  relationName?: string;
  relationDesc?: string;
}

// 字典配置接口
export interface DictConfig {
  type: DictType;
  label: string;
  codeField: string;
  nameField: string;
  descField: string;
  addButtonText: string;
  modalTitle: {
    add: string;
    edit: string;
  };
}

// 树形选择数据接口
export interface TreeSelectData {
  title: string;
  value: number;
  key: number;
  children?: TreeSelectData[];
}

// 树形显示数据接口
export interface TreeDisplayData {
  title: string;
  key: number;
  children: TreeDisplayData[];
  data: DictItem;
}

// 组件状态接口
export interface DictState {
  // 数据状态
  regionData: RegionDict[];
  typeData: TypeDict[];
  relationData: RelationshipDict[];
  
  // UI状态
  modalVisible: boolean;
  editingItem: DictItem | null;
  currentTab: DictType;
  loading: boolean;
  searchKeyword: string;
  statusFilter: number | undefined;
  
  // 批量操作状态
  selectedRowKeys: React.Key[];
  batchLoading: boolean;
  
  // 父级选项数据
  parentOptions: TreeSelectData[];
}

// 操作类型
export type ActionType = 'add' | 'edit' | 'delete' | 'toggle' | 'batchUpdate';

// API响应接口
export interface ApiResponse<T = any> {
  errCode: number;
  msg?: string;
  data?: T;
}

// 批量更新参数接口
export interface BatchUpdateParams {
  ids: number[];
  status: number;
}
