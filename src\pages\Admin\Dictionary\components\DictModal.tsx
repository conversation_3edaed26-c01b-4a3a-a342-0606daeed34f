import React from 'react';
import { Modal } from 'antd';
import type { FormInstance } from 'antd';
import type { DictType, TreeSelectData } from '../dict-types';
import { DictForm } from './DictForm';

interface DictModalProps {
  visible: boolean;
  title: string;
  loading: boolean;
  form: FormInstance;
  type: DictType;
  parentOptions: TreeSelectData[];
  onOk: () => void;
  onCancel: () => void;
}

export const DictModal: React.FC<DictModalProps> = ({
  visible,
  title,
  loading,
  form,
  type,
  parentOptions,
  onOk,
  onCancel,
}) => {
  return (
    <Modal
      title={title}
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={600}
      destroyOnClose
      confirmLoading={loading}
    >
      <DictForm
        form={form}
        type={type}
        parentOptions={parentOptions}
      />
    </Modal>
  );
};
