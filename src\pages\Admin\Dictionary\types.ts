// 字典管理相关类型定义

export type DictionaryType = 'region' | 'type' | 'relation';

export interface DictionaryItem {
  id: number;
  status: number;
  sort: number;
  parentId?: number | null;
  createdAt?: string;
  updatedAt?: string;
  children?: DictionaryItem[];
}

export interface RegionDictItem extends DictionaryItem {
  regionCode: string;
  regionName: string;
  regionDesc?: string;
}

export interface TypeDictItem extends DictionaryItem {
  typeCode: string;
  typeName: string;
  typeDesc?: string;
}

export interface RelationDictItem extends DictionaryItem {
  relationCode: string;
  relationName: string;
  relationDesc?: string;
}

export interface PaginationState {
  current: number;
  pageSize: number;
  total: number;
}

export interface TreeSelectOption {
  title: string;
  value: number;
  key: number;
  children?: TreeSelectOption[];
}

export interface DictionaryFormData {
  regionCode?: string;
  regionName?: string;
  regionDesc?: string;
  typeCode?: string;
  typeName?: string;
  typeDesc?: string;
  relationCode?: string;
  relationName?: string;
  relationDesc?: string;
  parentId?: number | null;
  status: number;
  sort: number;
}

export interface ToolbarProps {
  type: DictionaryType;
  viewMode: 'list' | 'tree';
  selectedRowKeys: React.Key[];
  batchLoading: boolean;
  onAdd: (type: DictionaryType) => void;
  onViewModeChange: (mode: 'list' | 'tree') => void;
  onSearch: (value: string) => void;
  onStatusFilterChange: (value: number | undefined) => void;
  onRefresh: () => void;
  onBatchStatusUpdate: (status: number) => void;
  onClearSelection: () => void;
}

export interface TreeViewProps {
  data: DictionaryItem[];
  type: DictionaryType;
  onEdit: (record: DictionaryItem, type: DictionaryType) => void;
  onDelete: (id: number, type: DictionaryType) => void;
}

export interface DictionaryFormProps {
  visible: boolean;
  loading: boolean;
  editingItem: DictionaryItem | null;
  currentTab: DictionaryType;
  parentOptions: TreeSelectOption[];
  onSubmit: () => void;
  onCancel: () => void;
  form: any;
}
