import React from 'react';
import { Button, Input, Select, Space } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import type { DictType } from '../dict-types';
import { DICT_CONFIGS, STATUS_OPTIONS, SEARCH_CONFIG, BATCH_CONFIG } from '../constants';

const { Option } = Select;

interface DictToolbarProps {
  type: DictType;
  searchKeyword: string;
  statusFilter: number | undefined;
  selectedRowKeys: React.Key[];
  batchLoading: boolean;
  onAdd: (type: DictType) => void;
  onSearch: (value: string) => void;
  onStatusFilterChange: (value: number | undefined) => void;
  onRefresh: (type: DictType) => void;
  onBatchStatusUpdate: (status: number) => void;
  onClearSelection: () => void;
}

export const DictToolbar: React.FC<DictToolbarProps> = ({
  type,
  searchKeyword,
  statusFilter,
  selectedRowKeys,
  batchLoading,
  onAdd,
  onSearch,
  onStatusFilterChange,
  onRefresh,
  onBatchStatusUpdate,
  onClearSelection,
}) => {
  const config = DICT_CONFIGS[type];

  return (
    <div style={{ marginBottom: 16 }}>
      {/* 主工具栏 */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 8,
        }}
      >
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => onAdd(type)}
          >
            {config.addButtonText}
          </Button>
        </Space>
        <Space>
          <Input.Search
            placeholder={SEARCH_CONFIG.placeholder}
            allowClear
            style={{ width: SEARCH_CONFIG.width }}
            value={searchKeyword}
            onSearch={onSearch}
            onChange={(e) => onSearch(e.target.value)}
          />
          <Select
            placeholder="状态筛选"
            allowClear
            style={{ width: 120 }}
            value={statusFilter}
            onChange={onStatusFilterChange}
          >
            {STATUS_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => onRefresh(type)}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 批量操作栏 */}
      {selectedRowKeys.length > 0 && (
        <div
          style={{
            marginBottom: 16,
            padding: '8px 16px',
            background: '#f0f2f5',
            borderRadius: '6px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Space>
            <span>已选择 {selectedRowKeys.length} 项</span>
            <Button size="small" onClick={onClearSelection}>
              取消选择
            </Button>
          </Space>
          <Space>
            <Button
              size="small"
              loading={batchLoading}
              onClick={() => onBatchStatusUpdate(BATCH_CONFIG.enable.status)}
            >
              {BATCH_CONFIG.enable.text}
            </Button>
            <Button
              size="small"
              loading={batchLoading}
              onClick={() => onBatchStatusUpdate(BATCH_CONFIG.disable.status)}
            >
              {BATCH_CONFIG.disable.text}
            </Button>
          </Space>
        </div>
      )}
    </div>
  );
};
