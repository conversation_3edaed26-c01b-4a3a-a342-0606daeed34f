/**
 * @file 字典管理常量配置
 * @description 包含字典类型配置、表格配置、表单规则、消息提示等常量定义
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { TablePaginationConfig } from 'antd';
import type { DictType, DictConfig } from './dict-types';

// 字典类型配置
export const DICT_CONFIGS: Record<DictType, DictConfig> = {
  region: {
    type: 'region',
    label: '区域字典',
    codeField: 'regionCode',
    nameField: 'regionName',
    descField: 'regionDesc',
    addButtonText: '添加区域',
    modalTitle: {
      add: '添加区域字典',
      edit: '编辑区域字典',
    },
  },
  type: {
    type: 'type',
    label: '类型字典',
    codeField: 'typeCode',
    nameField: 'typeName',
    descField: 'typeDesc',
    addButtonText: '添加类型',
    modalTitle: {
      add: '添加类型字典',
      edit: '编辑类型字典',
    },
  },
  relation: {
    type: 'relation',
    label: '关系字典',
    codeField: 'relationCode',
    nameField: 'relationName',
    descField: 'relationDesc',
    addButtonText: '添加关系',
    modalTitle: {
      add: '添加关系字典',
      edit: '编辑关系字典',
    },
  },
};

// 状态选项
export const STATUS_OPTIONS = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 表格通用配置
export const TABLE_CONFIG = {
  pagination: false as unknown as TablePaginationConfig,
  size: 'middle' as const,
  scroll: { x: 'max-content' },
};

// 表单默认值
export const FORM_DEFAULT_VALUES = {
  status: 1,
  parentId: null,
  sort: 1,
};

// 表单验证规则
export const FORM_RULES = {
  code: [
    { required: true, message: '请输入编码' },
    { max: 50, message: '编码长度不能超过50个字符' },
  ],
  name: [
    { required: true, message: '请输入名称' },
    { max: 255, message: '名称长度不能超过255个字符' },
  ],
  sort: [
    { required: true, message: '请输入排序号' },
  ],
};

// 搜索配置
export const SEARCH_CONFIG = {
  placeholder: '搜索编码或名称',
  width: 200,
};

// 批量操作配置
export const BATCH_CONFIG = {
  enable: { status: 1, text: '批量启用' },
  disable: { status: 0, text: '批量禁用' },
};

// 消息提示文本
export const MESSAGES = {
  loadError: {
    region: '加载区域字典失败',
    type: '加载类型字典失败',
    relation: '加载关系字典失败',
  },
  success: {
    add: '添加成功！',
    edit: '编辑成功！',
    delete: '删除成功！',
    statusUpdate: '状态更新成功',
    batchEnable: '批量启用成功',
    batchDisable: '批量禁用成功',
  },
  error: {
    add: '添加失败',
    edit: '编辑失败',
    delete: '删除失败',
    statusUpdate: '状态更新失败',
    batchOperation: '批量操作失败',
  },
  confirm: {
    delete: '确定要删除这条记录吗？',
  },
  warning: {
    selectItems: '请选择要操作的记录',
  },
};

// 表格列宽配置
export const COLUMN_WIDTHS = {
  id: 80,
  code: 220,
  sort: 80,
  status: 100,
  action: 150,
};
